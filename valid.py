"""Pydantic models for validating data."""

import json
from datetime import datetime
from typing import Optional, Union
from pydantic import BaseModel, Field, field_serializer, field_validator


class MetadataSerializer(BaseModel):
    """Pydantic model for formatting in validator the metadata."""

    metadata: Optional[dict] = None

    @field_serializer("metadata")
    def serialize_metadata(self, value: Optional[dict]) -> str:
        """Serialize metadata."""
        return json.dumps(value)


class LotsModel(MetadataSerializer, BaseModel):
    """Pydantic model for Lots class."""

    lot_id: str = Field(max_length=11)
    mask_set_id: str = Field(max_length=10)
    pending_mapping: bool
    metadata: Optional[dict] = None

    @field_validator("lot_id")
    @classmethod
    def no_spaces(cls, v):
        """Validate lot_id to ensure it does not contain spaces."""
        if " " in v:
            raise ValueError("lot_id must not contain spaces")
        return v


class WafersModel(MetadataSerializer, BaseModel):
    """Pydantic model for Wafers class."""

    wafer_id: str = Field(max_length=15)
    size: int
    metadata: Optional[dict] = None
    lot_id: str = Field(max_length=11)

    @field_validator("wafer_id")
    @classmethod
    def no_spaces(cls, v):
        """Validate wafer_id to ensure it does not contain spaces."""
        if " " in v:
            raise ValueError("wafer_id must not contain spaces")
        return v


class XfabFRLotsModel(BaseModel):
    """Pydantic model for XfabFRLots class."""

    xfab_fr_lot_id: str = Field(max_length=20)
    lot_id: str = Field(max_length=11)
    xfab_device_id: Optional[str] = Field(default=None, max_length=20)
    mes_lot_id: Optional[str] = Field(default=None, max_length=20)
    customer_device: Optional[str] = Field(default=None, max_length=50)


class XfabDELotsModel(BaseModel):
    """Pydantic model for XfabDELots class."""

    xfab_de_lot_id: str = Field(max_length=20)
    lot_id: str = Field(max_length=11)
    xfab_device_id: Optional[str] = Field(default=None, max_length=20)
    customer_device: Optional[str] = Field(default=None, max_length=50)


class ChipsModel(BaseModel):
    """Pydantic model for Chips class."""

    chip_id: Optional[int] = None
    chip_cell_id: int
    field_id: int
    wafer_id: str = Field(max_length=15)
