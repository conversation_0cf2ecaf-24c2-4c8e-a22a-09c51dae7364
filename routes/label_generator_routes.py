"""Label Generator routes for Talaria Dashboard."""

import os
import time
import uuid
from typing import Dict

from flask import Blueprint, current_app, jsonify, render_template, request, send_file
from flask_wtf.csrf import generate_csrf

# Import Talaria's custom authentication system
from core.auth.auth import admin_required, login_required

# Import the core label generation logic
from core.services.label_generator_service import LabelGeneratorService

# Create blueprint
label_generator_bp = Blueprint(
    "label_generator", __name__, url_prefix="/label-generator"
)

# Store generated files in memory for quick access
# This will store {session_id: {"label": label_path, "packing": packing_path, "timestamp": time.time()}}
GENERATED_FILES: Dict[str, Dict] = {}


@label_generator_bp.route("/")
@login_required
@admin_required
def index():
    """Render the label generator form page."""
    return render_template("label_generator.html", csrf_token=generate_csrf())


@label_generator_bp.route("/test", methods=["GET"])
@login_required
@admin_required
def test_route():
    """Test route to verify the blueprint is working."""
    return jsonify({"success": True, "message": "Label generator route is working!"})


@label_generator_bp.route("/debug/sessions", methods=["GET"])
@login_required
@admin_required
def debug_sessions():
    """Debug endpoint to check current sessions."""
    current_time = time.time()
    session_info = {}

    for sid, data in GENERATED_FILES.items():
        age_minutes = (current_time - data["timestamp"]) / 60
        session_info[sid] = {
            "age_minutes": round(age_minutes, 2),
            "label_exists": os.path.exists(data.get("label", "")),
            "packing_exists": os.path.exists(data.get("packing", "")),
            "label_filename": data.get("label_filename", ""),
            "packing_filename": data.get("packing_filename", ""),
        }

    return jsonify(
        {
            "success": True,
            "total_sessions": len(GENERATED_FILES),
            "sessions": session_info,
        }
    )


@label_generator_bp.route("/generate", methods=["POST"])
@login_required
@admin_required
def generate_label():
    """Generate a label based on form inputs."""
    try:
        current_app.logger.info("Label generation request received")
        current_app.logger.info(f"Request form data: {dict(request.form)}")
        # Get form data
        form_data = {
            "label_type": request.form.get("label_type"),
            "shipment_date": request.form.get("shipment_date"),
            "project_name": request.form.get("project_name"),
            "wafer_number": int(request.form.get("wafer_number", 0)),
            "wafer_list": request.form.get("wafer_list"),
            "po": request.form.get("po", ""),
            "device_id": request.form.get("device_id", ""),
            "xfab_lot_id": request.form.get("xfab_lot_id", ""),
            "xfab_device_id": request.form.get("xfab_device_id", ""),
            "comments": request.form.get("comments", ""),
            "printer_ip": request.form.get("printer_ip", ""),
            "copy_number": int(request.form.get("copy_number", 0)),
            "download_type": request.form.get("download_type", "success"),
            "shipment_origin": request.form.get(
                "shipment_origin", "france"
            ),  # Default to France
        }

        # Validate inputs for Erfurt label
        if "2" in form_data["label_type"] and (
            not form_data["po"]
            or not form_data["device_id"]
            or not form_data["xfab_lot_id"]
            or not form_data["xfab_device_id"]
        ):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "For Erfurt label, please fill the PO, XFAB lot, Project ID, and XFAB Device ID fields!",
                    }
                ),
                400,
            )

        # Initialize label generator service
        current_app.logger.info("Initializing LabelGeneratorService...")
        service = LabelGeneratorService()
        current_app.logger.info("Service initialized successfully")

        # Generate the label
        current_app.logger.info("Starting label generation...")
        result = service.generate_label(form_data)
        current_app.logger.info(
            f"Label generation result: {result.get('success', False)}"
        )

        if not result["success"]:
            return jsonify(result), 500

        # Generate a unique session ID for this label generation
        session_id = str(uuid.uuid4())

        # Store the file paths in memory with the session ID
        GENERATED_FILES[session_id] = {
            "label": result["label_path"],
            "packing": result["packing_path"],
            "timestamp": time.time(),
            "print_success": result["print_success"],
            "copies": form_data["copy_number"],
            "label_filename": os.path.basename(result["label_path"]),
            "packing_filename": os.path.basename(result["packing_path"]),
            "origin": form_data["shipment_origin"],  # Store the origin used
        }

        # Clean up old files (older than 2 hours to give more time for downloads)
        _cleanup_old_files()

        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                "label_filename": os.path.basename(result["label_path"]),
                "packing_filename": os.path.basename(result["packing_path"]),
                "print_success": result["print_success"],
                "copies": form_data["copy_number"],
                "message": "Label generated successfully!",
            }
        )

    except Exception as e:
        current_app.logger.error(f"Error generating label: {str(e)}")
        return (
            jsonify({"success": False, "message": f"An error occurred: {str(e)}"}),
            500,
        )


@label_generator_bp.route("/download/<file_type>")
@login_required
@admin_required
def download_file(file_type):
    """Download a generated file."""
    session_id = request.args.get("session_id")

    # Enhanced logging for debugging
    current_app.logger.info(
        f"Download request for file_type: {file_type}, session_id: {session_id}"
    )
    current_app.logger.info(f"Available sessions: {list(GENERATED_FILES.keys())}")

    if not session_id:
        current_app.logger.error("No session_id provided in download request")
        return (
            jsonify(
                {
                    "success": False,
                    "message": "No session ID provided. Please generate a new label.",
                }
            ),
            400,
        )

    if session_id not in GENERATED_FILES:
        current_app.logger.error(f"Session {session_id} not found in GENERATED_FILES")
        current_app.logger.info(f"Current sessions: {list(GENERATED_FILES.keys())}")
        return (
            jsonify(
                {
                    "success": False,
                    "message": "Session expired or invalid. Please generate a new label.",
                }
            ),
            404,
        )

    try:
        # Get file path based on type
        session_data = GENERATED_FILES[session_id]
        current_app.logger.info(f"Session data found: {session_data}")

        if file_type == "packing":
            file_path = session_data["packing"]
            filename = session_data["packing_filename"]
        else:  # Default to label
            file_path = session_data["label"]
            filename = session_data["label_filename"]

        current_app.logger.info(f"Attempting to download file: {file_path}")

        # Check if file exists
        if not os.path.exists(file_path):
            current_app.logger.error(f"File not found at path: {file_path}")
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "File not found. Please generate a new label.",
                    }
                ),
                404,
            )

        current_app.logger.info(f"Sending file: {filename}")

        # Return the file
        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype="application/pdf",
        )

    except KeyError as e:
        current_app.logger.error(f"Missing key in session data: {str(e)}")
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Session data incomplete. Missing: {str(e)}",
                }
            ),
            500,
        )
    except Exception as e:
        current_app.logger.error(f"Error downloading file: {str(e)}")
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error downloading file: {str(e)}",
                }
            ),
            500,
        )


@label_generator_bp.route("/success")
@login_required
@admin_required
def success():
    """Show success page with download links."""
    session_id = request.args.get("session_id")

    if not session_id or session_id not in GENERATED_FILES:
        return render_template(
            "label_generator.html",
            error="Session expired. Please generate a new label.",
        )

    file_data = GENERATED_FILES[session_id]

    return render_template(
        "label_generator_success.html", session_id=session_id, **file_data
    )


def _cleanup_old_files():
    """Clean up old files (older than 2 hours)."""
    current_time = time.time()
    expired_sessions = []

    for sid, data in GENERATED_FILES.items():
        if current_time - data["timestamp"] > 7200:  # 2 hours (7200 seconds)
            expired_sessions.append(sid)

    for sid in expired_sessions:
        # Try to remove files from disk
        try:
            file_data = GENERATED_FILES[sid]
            for file_path in [file_data.get("label"), file_data.get("packing")]:
                if file_path and os.path.exists(file_path):
                    os.remove(file_path)
        except Exception as e:
            current_app.logger.warning(
                f"Could not clean up files for session {sid}: {str(e)}"
            )

        # Remove from memory
        GENERATED_FILES.pop(sid, None)


@label_generator_bp.route("/download-packing-slip")
@login_required
@admin_required
def download_packing_slip_with_origin():
    """Download packing slip with specific origin."""
    try:
        session_id = request.args.get("session_id")
        origin = request.args.get("origin", "france")

        if origin not in ["france", "switzerland"]:
            origin = "france"

        if not session_id or session_id not in GENERATED_FILES:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Session not found. Please generate a new label.",
                    }
                ),
                404,
            )

        file_info = GENERATED_FILES[session_id]
        packing_path = file_info.get("packing")

        if not packing_path or not os.path.exists(packing_path):
            return jsonify({"success": False, "message": "Packing slip not found"}), 404

        # Note: For now, we return the existing packing slip
        # In the future, we could regenerate with different origin
        return send_file(
            packing_path,
            as_attachment=True,
            download_name=os.path.basename(packing_path),
            mimetype="application/pdf",
        )

    except Exception as e:
        current_app.logger.error(f"Error downloading packing slip: {str(e)}")
        return jsonify({"success": False, "message": f"Download error: {str(e)}"}), 500
